/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.sencha.tools.compiler.ast.js.*;

/**
 * A simplified closure converter that only parses essential AST nodes needed for
 * basic property checks (extend, mixins, requires, etc.) and represents some
 * non-essential constructs as SimplePassthroughNode instances.
 *
 * <p>This converter is designed to minimize AST complexity by only creating detailed
 * nodes for the following essential types:</p>
 * <ul>
 *   <li>ObjectLiteral - for class configuration objects</li>
 *   <li>ObjectProperty - for individual properties like extend, mixins, etc.</li>
 *   <li>StringLiteral - for string values in these properties</li>
 *   <li>ArrayLiteral - for array values (like multiple requires/mixins)</li>
 *   <li>PropertyGet - for property access expressions</li>
 *   <li>Name - for identifiers</li>
 *   <li>RootNode - for the program root</li>
 *   <li>ExpressionStatement - commonly expected by the codebase</li>
 *   <li>FunctionCall - for Ext.define calls</li>
 * </ul>
 *
 * <p>Only truly non-essential JavaScript constructs are represented as SimplePassthroughNode 
 * instances that preserve the original source text without detailed AST parsing.</p>
 *
 * @since 7.0.0
 */
public class SimpleClosureConverter extends BasicClosureConverter {

    /**
     * Creates a simple passthrough node that preserves the original source
     * without detailed AST parsing.
     */
    private BaseNode createSimplePassthroughNode(ParseTree tree) {
        SimplePassthroughNode node = new SimplePassthroughNode();
        node.setOriginalSource(extractOriginalSource(tree));
        node.setNodeType(tree.getClass().getSimpleName());
        return node;
    }

    /**
     * Extracts the original source code for a parse tree node.
     * Uses reflection to access the private getOriginalSource method from BasicClosureConverter.
     */
    private String extractOriginalSource(ParseTree tree) {
        if (tree == null || tree.location == null) {
            return "";
        }

        try {
            // Use reflection to access the private getOriginalSource method
            java.lang.reflect.Method method = BasicClosureConverter.class.getDeclaredMethod("getOriginalSource", ParseTree.class);
            method.setAccessible(true);
            return (String) method.invoke(this, tree);
        } catch (Exception e) {
            // Fallback: return empty string if reflection fails
            return "";
        }
    }

    // Essential nodes for property checks - use detailed parsing

    @Override
    public BaseNode convert(ProgramTree tree) {
        // Always parse the root node properly
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ObjectLiteralExpressionTree tree) {
        // Always parse object literals properly for class configurations
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(PropertyNameAssignmentTree tree) {
        // Always parse object properties properly
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(LiteralExpressionTree tree) {
        // Always parse string literals properly
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ArrayLiteralExpressionTree tree) {
        // Always parse array literals properly for requires/mixins arrays
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(MemberExpressionTree tree) {
        // Always parse property access properly
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(IdentifierExpressionTree tree) {
        // Always parse identifiers properly
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ExpressionStatementTree tree) {
        // ExpressionStatement is often expected by the codebase - delegate to parent
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(CallExpressionTree tree) {
        // Function calls (like Ext.define) are essential - always create FunctionCall nodes
        // Don't check for modern syntax to avoid PassthroughNode creation
        FunctionCall node = new FunctionCall();
        node.setTarget(doConvert(tree.operand, node));
        for(ParseTree arg: tree.arguments.arguments) {
            BaseNode convertedArg = doConvert(arg, node);
            node.addArgument(convertedArg);
        }
        return node;
    }

    @Override
    public BaseNode convert(ArgumentListTree tree) {
        // Argument lists are essential for function calls - delegate to parent
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(com.google.javascript.jscomp.parsing.parser.trees.Comment tree) {
        // Comments need special handling - delegate to parent
        return super.convert(tree);
    }

    // Selective passthrough for truly non-essential constructs

    @Override
    public BaseNode convert(ArrayPatternTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(AwaitExpressionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ClassDeclarationTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(DefaultParameterTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ForAwaitOfStatementTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ForOfStatementTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ImportDeclarationTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ImportSpecifierTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ExportDeclarationTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ExportSpecifierTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ObjectPatternTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(ObjectSpreadTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(OptionalMemberExpressionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(OptionalMemberLookupExpressionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(TemplateLiteralExpressionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(TemplateLiteralPortionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(TemplateSubstitutionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    @Override
    public BaseNode convert(YieldExpressionTree tree) {
        return createSimplePassthroughNode(tree);
    }

    // Additional required methods from ClosureASTConverter interface - delegate to parent for safety

    @Override
    public BaseNode convert(BinaryOperatorTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(BlockTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(BreakStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(CaseClauseTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(CatchTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(CommaExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComprehensionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComprehensionForTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComprehensionIfTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComputedPropertyDefinitionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComputedPropertyGetterTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComputedPropertyMethodTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ComputedPropertySetterTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ConditionalExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ContinueStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(DebuggerStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(DefaultClauseTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(DoWhileStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(DynamicImportTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(EmptyStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(FieldDeclarationTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(FinallyTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ForInStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ForStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(FormalParameterListTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(FunctionDeclarationTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(GetAccessorTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(IfStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ImportMetaExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(IterRestTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(IterSpreadTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(LabelledStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(MemberLookupExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(MissingPrimaryExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(NewExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(NewTargetExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(NullTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ObjectRestTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(OptChainCallExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ParenExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ParseTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ReturnStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(SetAccessorTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(SuperExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(SwitchStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ThisExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(ThrowStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(TryStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(UnaryExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(UpdateExpressionTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(VariableDeclarationListTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(VariableDeclarationTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(VariableStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(WhileStatementTree tree) {
        return super.convert(tree);
    }

    @Override
    public BaseNode convert(WithStatementTree tree) {
        return super.convert(tree);
    }
}
